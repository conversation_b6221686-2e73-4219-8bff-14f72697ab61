<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BaseUpload 测试</title>
</head>
<body>
    <h1>BaseUpload 组件功能测试</h1>
    
    <h2>测试场景</h2>
    <ol>
        <li><strong>初始化测试</strong>：当 fileIdList 有值时，应该自动生成对应的 fileList</li>
        <li><strong>上传测试</strong>：上传文件后，fileList 和 fileIdList 应该保持同步</li>
        <li><strong>删除测试</strong>：删除文件后，两个列表应该保持同步</li>
        <li><strong>避免循环触发</strong>：监听器不应该无限循环触发</li>
    </ol>
    
    <h2>关键功能点</h2>
    <ul>
        <li>✅ <code>syncFileIdList()</code>：从 fileList 提取 attachId 到 fileIdList</li>
        <li>✅ <code>syncFileListFromIds()</code>：根据 fileIdList 生成 fileList</li>
        <li>✅ 标志位控制：<code>isUpdatingFromFileList</code> 和 <code>isUpdatingFromFileIdList</code></li>
        <li>✅ 监听器：fileList 和 fileIdList 的双向监听</li>
        <li>✅ 初始化：fileIdList 监听器设置了 <code>immediate: true</code></li>
    </ul>
    
    <h2>预期行为</h2>
    <pre>
1. 组件初始化时，如果 fileIdList = [21, 22]
   → 自动调用 previewApi 获取 URL
   → 生成对应的 fileList，包含 attachId、status: 'done'、url 等属性

2. 用户上传新文件
   → handleUploadChange 更新 fileList
   → 自动同步到 fileIdList

3. 用户删除文件
   → fileList 变化
   → 自动同步到 fileIdList

4. 外部修改 fileIdList
   → 自动重新生成 fileList
    </pre>
    
    <h2>防循环机制</h2>
    <pre>
- isUpdatingFromFileList: 当从 fileList 同步到 fileIdList 时设置为 true
- isUpdatingFromFileIdList: 当从 fileIdList 同步到 fileList 时设置为 true
- 每个同步函数都会检查对应的标志位，避免重复触发
    </pre>
</body>
</html>
