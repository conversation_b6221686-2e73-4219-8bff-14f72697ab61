<script setup lang="ts">
import type { Ref } from 'vue';

import type { ConfigInfo } from '@vben/types';

import { inject, ref } from 'vue';

import { BaseUpload } from '@vben/base-ui';
import BasicHelp from '@vben/fe-ui/components/Basic/src/BasicHelp.vue';

import { Button, Col, Form, FormItem, Input, Row, Switch } from 'ant-design-vue';

import { getDownloadFileLinkApi, uploadFileA<PERSON> } from '#/api/core/file';
import {
  configInfoInjectionKey,
  configLoadingInjectionKey,
  configSaveInjectionKey,
} from '#/views/system/config/config-injection-key';

const configForm = inject(configInfoInjectionKey) as Ref<Partial<ConfigInfo>>;
const saveConfig = inject(configSaveInjectionKey) as Function;
const loading = inject(configLoadingInjectionKey) as Ref<{ save: boolean }>;

const systemFormRef = ref();
const rules = {};
const fileIdList = ref([10]);
const save = () => {
  saveConfig(configForm.value);
};
</script>

<template>
  <Row>
    <Col :span="12">
      <Form
        ref="systemFormRef"
        :model="configForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        :colon="false"
      >
        <!-- 系统LOGO -->
        <FormItem name="imgLogo">
          <template #label>
            系统LOGO
            <BasicHelp text="显示在登录页面和系统导航栏的网站图标" />
          </template>
          <BaseUpload v-model:link="configForm.imgLogo" :upload-api="uploadFileApi" accept="image/*" />
        </FormItem>
        <!-- 系统图标 -->
        <FormItem name="imgFavicon">
          <template #label>
            系统图标
            <BasicHelp text="浏览器标签页显示的网站图标" />
          </template>
          <BaseUpload v-model:link="configForm.imgFavicon" :upload-api="uploadFileApi" accept="image/*,.ico" />
        </FormItem>
        <!-- 登录背景 -->
        <FormItem name="imgLoginBackground">
          <template #label>
            登录背景
            <BasicHelp text="显示在登录页面的背景图片" />
          </template>
          <BaseUpload
            v-model:id-list="fileIdList"
            :upload-api="uploadFileApi"
            :preview-api="getDownloadFileLinkApi"
            show-upload-list
            :max-count="10"
            list-type="text"
          />
        </FormItem>
        <!-- 系统名称 -->
        <FormItem name="title">
          <template #label>
            系统名称
            <BasicHelp text="显示在浏览器标题栏、登录界面和导航栏的系统名称" />
          </template>
          <Input v-model:value="configForm.title" />
        </FormItem>
        <!-- 版权信息 -->
        <FormItem name="copyright">
          <template #label>
            版权信息
            <BasicHelp text="显示在页面底部的版权声明文本" />
          </template>
          <Input v-model:value="configForm.copyright" />
        </FormItem>
        <!-- 备案号 -->
        <FormItem name="icpLicense">
          <template #label>
            备案号
            <BasicHelp text="工信部 ICP 备案编号（如：京ICP备12345678号）" />
          </template>
          <Input v-model:value="configForm.icpLicense" />
        </FormItem>
        <!-- 导航栏显示 -->
        <FormItem name="isNavbarShow">
          <template #label>
            导航栏显示
            <BasicHelp text="是否在导航栏显示系统名称" />
          </template>
          <Switch v-model:checked="configForm.isNavbarShow" :checked-value="1" :un-checked-value="0" />
        </FormItem>
        <!-- 登录界面显示 -->
        <FormItem name="isLoginShow">
          <template #label>
            登录页显示
            <BasicHelp text="是否在登录页显示系统名称" />
          </template>
          <Switch v-model:checked="configForm.isLoginShow" :checked-value="1" :un-checked-value="0" />
        </FormItem>
        <FormItem class="text-right" :wrapper-col="{ span: 24 }">
          <Button type="primary" :loading="loading.save" @click="save">保存</Button>
        </FormItem>
      </Form>
      <!--<BaseForm :submit-button-options="{ loading: loading.save }" />-->
    </Col>
  </Row>
</template>

<style></style>
